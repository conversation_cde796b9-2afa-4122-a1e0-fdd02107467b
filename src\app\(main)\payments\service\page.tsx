"use client"

import React, { useState, useMemo, useRef } from 'react';
import { Download, Plus, Trash2, Edit2, Save, X, Printer, Loader2, Banknote } from 'lucide-react';
import { 
  usePaymentRequests,
  useCreatePaymentRequest,
  useCreatePayment,
  useUpdatePayment,
  useDeletePayment,
  useToggleAccountInfo,
  useCreateBankAccount,
  useUpdateBankAccount,
  useDeleteBankAccount, 
  PaymentAccountType,
  type PaymentRequest,
  type Payment,
} from '@/hooks/use-payment-requests';


const PaymentManager = () => {
  const [selectedPaymentRequest, setSelectedPaymentRequest] = useState<PaymentRequest | null>(null);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editForm, setEditForm] = useState<Partial<Payment>>({});
  const [showBankAccounts, setShowBankAccounts] = useState(false);
  
  const [paymentRequestForm, setPaymentRequestForm] = useState({
    name: '僑星福華社區',
    year: '114',
    month: '08'
  });

  const printRef = useRef<HTMLDivElement>(null);

  // React Query hooks
  const { 
    data: paymentRequests = [], 
    isLoading,
    error,
    refetch: refetchPaymentRequests
  } = usePaymentRequests();
  
  const createPaymentRequestMutation = useCreatePaymentRequest();
  const createPaymentMutation = useCreatePayment();
  const updatePaymentMutation = useUpdatePayment();
  const deletePaymentMutation = useDeletePayment();
  const toggleAccountInfoMutation = useToggleAccountInfo();
  
  // Assuming these hooks work with PaymentAccount
  const createPaymentAccountMutation = useCreateBankAccount();
  const updatePaymentAccountMutation = useUpdateBankAccount();
  const deletePaymentAccountMutation = useDeleteBankAccount();

  // Auto-select first payment request when data loads
  React.useEffect(() => {
    if (paymentRequests.length > 0 && !selectedPaymentRequest) {
      const firstRequest = paymentRequests[0];
      setSelectedPaymentRequest(firstRequest);
      setPaymentRequestForm({ name: firstRequest.name, year: firstRequest.year, month: firstRequest.month });
    }
  }, [paymentRequests, selectedPaymentRequest]);

  const handleCreatePaymentRequest = async () => {
    try {
      const result = await createPaymentRequestMutation.mutateAsync(paymentRequestForm);
      if (result.success && result.data) {
        await refetchPaymentRequests();
        setSelectedPaymentRequest(result.data);
      } else {
        alert(result.error);
      }
    } catch(e) {
      alert('創建失敗，請檢查該月份是否已存在。');
    }
  };

  const handleAddPayment = async () => {
    if (!selectedPaymentRequest) {
      alert('請先選擇或創建請款單');
      return;
    }

    const existingIds = selectedPaymentRequest.payments.map(p => parseInt(p.sequenceId));
    const nextId = existingIds.length > 0 ? Math.max(...existingIds) + 1 : 1140801;
    
    const newPayment = {
      sequenceId: nextId.toString(),
      accountingSubject: '',
      payee: '',
      month: `${selectedPaymentRequest.year}/${selectedPaymentRequest.month}`,
      amount: 0,
      paymentMethod: '匯款',
      showAccountInfo: false,
      paymentRequestId: selectedPaymentRequest.id
    };

    const result = await createPaymentMutation.mutateAsync(newPayment);
    
    if (result.success && result.data) {
      setEditingId(result.data.id);
      setEditForm(result.data);
    } else {
      alert(result.error);
    }
  };

  const handleDeletePayment = async (id: string) => {
    if (!confirm('確定要刪除此筆付款記錄嗎？')) return;
    await deletePaymentMutation.mutateAsync(id);
  };

  const startEdit = (payment: Payment) => {
    setEditingId(payment.id);
    setEditForm({ ...payment });
  };

  const handleSaveEdit = async () => {
    if (!editingId || !editForm) return;
    const { id, createdAt, updatedAt, paymentRequestId, remitterAccount, payeeAccount, ...dataToUpdate } = editForm as any;
    await updatePaymentMutation.mutateAsync({ id: editingId, data: dataToUpdate });
    setEditingId(null);
    setEditForm({});
  };

  const cancelEdit = () => {
    setEditingId(null);
    setEditForm({});
  };

  const handleToggleAccountInfo = async (payment: Payment) => {
    await toggleAccountInfoMutation.mutateAsync({ paymentId: payment.id, show: !payment.showAccountInfo });
  };

  const exportToCSV = () => {
    if (!selectedPaymentRequest) return;
    const headers = ['序號', '會計科目', '支出摘要/受款人', '月份', '金額', '付款方式', '受款人銀行代號', '受款人銀行', '受款人分行', '受款人帳號', '受款人戶名'];
    const csvContent = [
        headers.join(','), 
        ...selectedPaymentRequest.payments.map(p => [
            p.sequenceId, 
            `"${p.accountingSubject}"`, 
            `"${p.payee}"`, 
            p.month, p.amount, 
            `"${p.paymentMethod}"`, 
            p.payeeAccount?.bankCode || '', 
            `"${p.payeeAccount?.bankName || ''}"`, 
            `"${p.payeeAccount?.branchName || ''}"`, 
            p.payeeAccount?.accountNumber || '', 
            `"${p.payeeAccount?.accountName || ''}"`
        ].join(','))
    ].join('\n');
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `${selectedPaymentRequest.name}_${selectedPaymentRequest.year}年${selectedPaymentRequest.month}月份請款明細表.csv`;
    link.click();
  };

  const printSummary = () => window.print();

  const defaultRemitterAccount = useMemo(() => {
    return selectedPaymentRequest?.paymentAccounts.find(acc => acc.isDefault && (acc.accountType === PaymentAccountType.REMITTER || acc.accountType === PaymentAccountType.BOTH));
  }, [selectedPaymentRequest]);

  const generateRemittanceSlip = (payment: Payment) => {
    const remitter = payment.remitterAccount || defaultRemitterAccount;
    const payee = payment.payeeAccount;

    if (!payee) return null; // Cannot generate without a payee
    if (!remitter) return <div key={payment.id} className="text-red-500 p-4">無法產生匯款單: 付款 {payment.sequenceId} 未指定匯款人帳戶，且無預設匯款帳戶。</div>

    return (
      <div key={payment.id} className="page-break border-2 border-gray-400 p-4 mb-6 bg-white">
        <h2 className="text-center text-lg font-bold mb-4 border-b pb-2">國泰世華商業銀行 匯出匯款憑證</h2>
        <div className="grid grid-cols-12 gap-2 text-sm">
          <div className="col-span-2 bg-gray-100 p-2 font-bold">匯款人</div>
          <div className="col-span-4 border p-2">{remitter.accountName}</div>
          <div className="col-span-2 bg-gray-100 p-2 font-bold">匯款帳號</div>
          <div className="col-span-4 border p-2 font-mono">{remitter.accountNumber}</div>

          <div className="col-span-2 bg-gray-100 p-2 font-bold">受款銀行</div>
          <div className="col-span-4 border p-2">{payee.bankName} ({payee.bankCode})</div>
          <div className="col-span-2 bg-gray-100 p-2 font-bold">受款分行</div>
          <div className="col-span-4 border p-2">{payee.branchName}</div>
          
          <div className="col-span-2 bg-gray-100 p-2 font-bold">受款人帳號</div>
          <div className="col-span-4 border p-2 font-mono">{payee.accountNumber}</div>
          <div className="col-span-2 bg-gray-100 p-2 font-bold">匯款金額</div>
          <div className="col-span-4 border p-2 text-right font-bold">{payment.amount.toLocaleString()}</div>
          
          <div className="col-span-2 bg-gray-100 p-2 font-bold">受款人戶名</div>
          <div className="col-span-10 border p-2">{payee.accountName}</div>
          
          <div className="col-span-2 bg-gray-100 p-2 font-bold">摘要</div>
          <div className="col-span-10 border p-2">{payment.accountingSubject}</div>
        </div>
      </div>
    );
  };

  const totalAmount = selectedPaymentRequest?.payments.reduce((sum, p) => sum + p.amount, 0) || 0;
  const isLoadingMutation = createPaymentRequestMutation.isPending || createPaymentMutation.isPending || updatePaymentMutation.isPending || deletePaymentMutation.isPending || toggleAccountInfoMutation.isPending;

  const remitterAccounts = useMemo(() => selectedPaymentRequest?.paymentAccounts.filter(acc => acc.isActive && (acc.accountType === PaymentAccountType.REMITTER || acc.accountType === PaymentAccountType.BOTH)) || [], [selectedPaymentRequest]);
  const payeeAccounts = useMemo(() => selectedPaymentRequest?.paymentAccounts.filter(acc => acc.isActive && (acc.accountType === PaymentAccountType.PAYEE || acc.accountType === PaymentAccountType.BOTH)) || [], [selectedPaymentRequest]);

  if (isLoading) return <div className="flex items-center justify-center min-h-screen"><Loader2 className="animate-spin" size={32} /><span className="ml-2">載入中...</span></div>;
  if (error) return <div className="flex items-center justify-center min-h-screen"><div className="text-red-500">載入失敗: {error.message}</div></div>;

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-6 bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold mb-4">社區請款管理系統</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label className="block text-sm font-medium mb-2">選擇請款單</label>
                <select
                    value={selectedPaymentRequest?.id || ''}
                    onChange={(e) => {
                    const req = paymentRequests.find(pr => pr.id === e.target.value) || null;
                    setSelectedPaymentRequest(req);
                    if(req) {
                        setPaymentRequestForm({ name: req.name, year: req.year, month: req.month });
                    }
                    }}
                    className="w-full border rounded px-3 py-2 mb-2"
                >
                    <option value="">選擇現有請款單...</option>
                    {paymentRequests.map(paymentRequest => (
                    <option key={paymentRequest.id} value={paymentRequest.id}>{paymentRequest.name} - {paymentRequest.year}年{paymentRequest.month}月</option>
                    ))}
                </select>
                <div className="grid grid-cols-3 gap-4 mb-4 border p-4 rounded-md">
                    <h3 className="col-span-3 text-lg font-semibold mb-2">新增請款單</h3>
                    <input type="text" placeholder="社區名稱" value={paymentRequestForm.name} onChange={(e) => setPaymentRequestForm({...paymentRequestForm, name: e.target.value})} className="border rounded px-3 py-2" />
                    <input type="text" placeholder="年度" value={paymentRequestForm.year} onChange={(e) => setPaymentRequestForm({...paymentRequestForm, year: e.target.value})} className="border rounded px-3 py-2" />
                    <input type="text" placeholder="月份" value={paymentRequestForm.month} onChange={(e) => setPaymentRequestForm({...paymentRequestForm, month: e.target.value})} className="border rounded px-3 py-2" />
                    <div className="col-span-3 flex justify-start">
                        <button onClick={handleCreatePaymentRequest} disabled={isLoadingMutation} className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50 flex items-center gap-2">
                        {isLoadingMutation ? <Loader2 className="animate-spin" size={16} /> : <Plus size={16} />} 新增請款單
                        </button>
                    </div>
                </div>
            </div>
            <div>
                <h3 className="text-lg font-semibold mb-2">操作</h3>
                <div className="flex flex-wrap gap-2">
                    <button onClick={handleAddPayment} disabled={!selectedPaymentRequest || isLoadingMutation} className="bg-blue-500 text-white px-4 py-2 rounded flex items-center gap-2 hover:bg-blue-600 disabled:opacity-50"><Plus size={16} />新增付款項目</button>
                    <button onClick={() => setShowBankAccounts(!showBankAccounts)} disabled={!selectedPaymentRequest} className="bg-yellow-500 text-white px-4 py-2 rounded flex items-center gap-2 hover:bg-yellow-600 disabled:opacity-50"><Banknote size={16} />管理帳戶</button>
                    <button onClick={exportToCSV} disabled={!selectedPaymentRequest} className="bg-green-500 text-white px-4 py-2 rounded flex items-center gap-2 hover:bg-green-600 disabled:opacity-50"><Download size={16} />匯出CSV</button>
                    <button onClick={printSummary} disabled={!selectedPaymentRequest} className="bg-purple-500 text-white px-4 py-2 rounded flex items-center gap-2 hover:bg-purple-600 disabled:opacity-50"><Printer size={16} />列印摘要</button>
                </div>
            </div>
        </div>
      </div>

      {showBankAccounts && selectedPaymentRequest && (
        <div className="mb-6 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold mb-4">管理帳戶 for {selectedPaymentRequest.name}</h2>
          {/* TODO: Implement UI for adding, editing, and deleting PaymentAccounts */}
          <div className="text-center text-gray-500">帳戶管理介面待完成</div>
        </div>
      )}

      {selectedPaymentRequest && (
        <>
          <div className="print:block">
            <div className="bg-white rounded-lg shadow-md mb-6 print:shadow-none">
              <div className="p-6">
                <div className="text-center text-xl font-bold mb-6 print:text-lg">{selectedPaymentRequest.name}{selectedPaymentRequest.year}年{selectedPaymentRequest.month}月份請款明細表</div>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300 text-sm">
                    <thead><tr className="bg-gray-100"><th className="border p-2">序號</th><th className="border p-2">會計科目</th><th className="border p-2">支出摘要/受款人</th><th className="border p-2">月份</th><th className="border p-2">金額</th><th className="border p-2">付款方式</th><th className="border p-2 print:hidden">帳戶資訊</th><th className="border p-2 print:hidden">操作</th></tr></thead>
                    <tbody>
                      {selectedPaymentRequest.payments.map((payment) => (
                        <React.Fragment key={payment.id}>
                          <tr>
                            <td className="border p-2">{editingId === payment.id ? <input type="text" value={editForm.sequenceId || ''} onChange={(e) => setEditForm({...editForm, sequenceId: e.target.value})} className="w-full border rounded p-1 text-sm" /> : payment.sequenceId}</td>
                            <td className="border p-2">{editingId === payment.id ? <input type="text" value={editForm.accountingSubject || ''} onChange={(e) => setEditForm({...editForm, accountingSubject: e.target.value})} className="w-full border rounded p-1 text-sm" /> : payment.accountingSubject}</td>
                            <td className="border p-2">{editingId === payment.id ? <input type="text" value={editForm.payee || ''} onChange={(e) => setEditForm({...editForm, payee: e.target.value})} className="w-full border rounded p-1 text-sm" /> : payment.payee}</td>
                            <td className="border p-2">{editingId === payment.id ? <input type="text" value={editForm.month || ''} onChange={(e) => setEditForm({...editForm, month: e.target.value})} className="w-full border rounded p-1 text-sm" /> : payment.month}</td>
                            <td className="border p-2 text-right">{editingId === payment.id ? <input type="number" value={editForm.amount || 0} onChange={(e) => setEditForm({...editForm, amount: parseInt(e.target.value) || 0})} className="w-full border rounded p-1 text-sm text-right" /> : `${payment.amount.toLocaleString()}`}</td>
                            <td className="border p-2">{editingId === payment.id ? <select value={editForm.paymentMethod || ''} onChange={(e) => setEditForm({...editForm, paymentMethod: e.target.value})} className="w-full border rounded p-1 text-sm"><option value="匯款□/領現">匯款□/領現</option><option value="自動扣款">自動扣款</option></select> : payment.paymentMethod}</td>
                            <td className="border p-2 print:hidden"><input type="checkbox" checked={!!payment.showAccountInfo} onChange={() => handleToggleAccountInfo(payment)} className="mr-2" disabled={isLoadingMutation} />顯示</td>
                            <td className="border p-2 print:hidden">{editingId === payment.id ? <div className="flex gap-1"><button onClick={handleSaveEdit} disabled={isLoadingMutation} className="p-1"><Save size={14} /></button><button onClick={cancelEdit} disabled={isLoadingMutation} className="p-1"><X size={14} /></button></div> : <div className="flex gap-1"><button onClick={() => startEdit(payment)} disabled={isLoadingMutation} className="p-1"><Edit2 size={14} /></button><button onClick={() => handleDeletePayment(payment.id)} disabled={isLoadingMutation} className="p-1"><Trash2 size={14} /></button></div>}</td>
                          </tr>
                          {payment.showAccountInfo && editingId !== payment.id && payment.payeeAccount && (
                            <tr className="bg-gray-50"><td className="border p-2" colSpan={8}><div className="grid grid-cols-5 gap-4 text-xs"><div><strong>受款銀行:</strong> {payment.payeeAccount.bankName} ({payment.payeeAccount.bankCode})</div><div><strong>分行:</strong> {payment.payeeAccount.branchName}</div><div><strong>帳號:</strong> {payment.payeeAccount.accountNumber}</div><div><strong>戶名:</strong> {payment.payeeAccount.accountName}</div></div></td></tr>
                          )}
                          {editingId === payment.id && (
                            <tr className="bg-blue-50"><td className="border p-2" colSpan={8}>
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="block text-xs font-semibold mb-1">匯款帳戶 (留空使用預設)</label>
                                  <select
                                    value={editForm.remitterAccountId || ''}
                                    onChange={(e) => setEditForm({...editForm, remitterAccountId: e.target.value || undefined })}
                                    className="w-full border rounded p-1 text-xs"
                                  >
                                    <option value="">使用請款單預設</option>
                                    {remitterAccounts.map(acc => <option key={acc.id} value={acc.id}>{acc.accountName} ({acc.bankName})</option>)}
                                  </select>
                                </div>
                                <div>
                                  <label className="block text-xs font-semibold mb-1">受款帳戶</label>
                                  <select
                                    value={editForm.payeeAccountId || ''}
                                    onChange={(e) => setEditForm({...editForm, payeeAccountId: e.target.value || undefined })}
                                    className="w-full border rounded p-1 text-xs"
                                  >
                                    <option value="">選擇受款帳戶</option>
                                    {payeeAccounts.map(acc => <option key={acc.id} value={acc.id}>{acc.accountName} ({acc.bankName})</option>)}
                                  </select>
                                </div>
                              </div>
                            </td></tr>
                          )}
                        </React.Fragment>
                      ))}
                    </tbody>
                    <tfoot><tr className="bg-gray-100 font-bold"><td className="border p-2" colSpan={4}>合計</td><td className="border p-2 text-right">${totalAmount.toLocaleString()}</td><td className="border p-2" colSpan={3}></td></tr></tfoot>
                  </table>
                </div>
                <div className="grid grid-cols-4 gap-4 mt-6 border border-gray-300"><div className="border-r p-4 text-center"><div className="font-bold mb-2">主任委員</div><div className="h-16"></div></div><div className="border-r p-4 text-center"><div className="font-bold mb-2">監察委員</div><div className="h-16"></div></div><div className="border-r p-4 text-center"><div className="font-bold mb-2">財務委員</div><div className="h-16"></div></div><div className="p-4 text-center"><div className="font-bold mb-2">承辦人</div><div className="h-16"></div><div className="text-red-500 text-sm border border-red-500 inline-block px-2 py-1 mt-2">行政總幹事</div></div></div>
              </div>
            </div>
          </div>
          <div className="print:hidden">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-bold mb-4">匯款憑證預覽</h2>
              <div ref={printRef}>{selectedPaymentRequest.payments.map(generateRemittanceSlip)}</div>
            </div>
          </div>
        </>
      )}
      <style jsx global>{`@media print { body * { visibility: hidden; } .print\:block, .print\:block * { visibility: visible; } .print\:hidden { display: none !important; } .print\:shadow-none { box-shadow: none !important; } .page-break { page-break-after: always; } @page { margin: 1cm; } }`}</style>
    </div>
  );
};

export default PaymentManager;
